import { Alert } from "antd";
import React, { useState } from "react";

import { Message } from "@/core/common/message";
import { MessageStatus, Role } from "@/types";

import { MessageRender } from "../render";
import Append from "./Append";
import MessageContext from "./Context";
import Footer from "./Footer";
import Header from "./Header";

interface BubbleProps {
  message: Message;
  messageRender?: (content: string) => React.ReactNode;
  avatar: {
    icon: React.ReactNode;
    style?: React.CSSProperties;
  };
  placement: "start" | "end";
  role: Role;
}

const Bubble: React.FC<BubbleProps> = (props) => {
  const { message, placement, role } = props;
  const [messageState, setMessageState] = useState<Record<string, any>>({});

  return (
    <MessageContext.Provider value={{ message, messageState, setMessageState }}>
      <div className={`ag:w-full ag:flex ${placement === "end" ? "ag:justify-end" : ""}`} hidden={message.hidden}>
        <div className={`${placement === "end" ? "ag:max-w-[80%]" : "ag:w-[80%]"} cscs-agent-bubble`}>
          <div className={`ag:py-2 ${placement === "end" ? "ag:bg-[rgba(37,45,62,0.06)] ag:p-3 ag:rounded-lg" : ""}`}>
            <Header />
            <MessageRender data={message} />
            {message.status === MessageStatus.Loading && <div className="ag:mt-4 ag:message-loader"></div>}
          </div>
          <Append role={role} />

          <div className="ag:mt-2 ag:max">
            {message.error && <Alert type="error" message={message.getErrorMessage()} showIcon />}
            {message.status === MessageStatus.Cancelled && (
              <span className="ag:text-black-45 ag:text-sm">已停止生成</span>
            )}
          </div>
          <div
            className={`ag:flex ag:mt-2 ${role === Role.HUMAN ? "ag:flex-row-reverse cscs-agent-bubble-footer-human" : ""}`}
          >
            <Footer role={role} />
          </div>
        </div>
      </div>
    </MessageContext.Provider>
  );
};

export default Bubble;
