## 开发指南

### 启动开发环境

```bash
# 安装依赖
pnpm i

# 预编译包
pnpm build:packages

# 启动项目
cd playground
pnpm dev

# 开发 package 
cd packages/{pkg}
pnpm watch
```

### 模板项目安装依赖

```bash
pnpm i -C template/basic --ignore-workspace
```

### 部署

Nginx 配置

```nginx
server {
		listen 5800;
		etag on;
		gzip on;
		charset utf-8;
		root /data/agent/web;

		location / {
			add_header Cache-Control no-cache;

			try_files $uri $uri/ /index.html;
		}

		location /api/ {
			proxy_pass http://************:5801;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header X-Real-IP $remote_addr;
		}

		location /cluster-api/ {
			proxy_pass http://************:8002/api/;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header X-Real-IP $remote_addr;
		}
}
```

### 版本发布

```bash
# 更新版本号
pnpm run build

# 发布
pnpm publish --filter "@cscs-agent/*"
```

## Todo List

- [x] 会话列表的分组功能
- [x] 会话列表的搜索功能
- [x] 会话列表的排序
- [x] 会话接口异常处理
- [x] 代码显示框样式优化
- [x] message slot
- [x] sender slot
- [ ] side panel slot
- [ ] 动态页面预览页面退出登录问题
- [x] 思维链支持
- [ ] StructuredMessagePackage 支持
- [x] 富文本输入框
- [x] 替换 sender
- [ ] 表单消息解析，集成 Formily 表单
- [ ] 样式主题统一管理
- [ ] LaTex 支持
- [x] cli 新建 widget
- [x] cli 新建 模板项目
- [x] cli 更新项目
- [x] mock server
- [ ] mini 模式
- [ ] vite preview
- [ ] 部分接口禁用 token 配置
- [ ] 部分接口忽略异常
- [ ] 移除 Moment
- [ ] 替换/移除 core 中 antd 组件
- [ ] Sender footer 组件位置配置
- [ ] 登录地址通过配置文件配置
- [ ] 扩展Message Slot 显示额外内容
- [ ] 发送消息时保存 ExtendData 到本地会话列表
